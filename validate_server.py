#!/usr/bin/env python3
"""
🦍 KING KONG'S SERVER VALIDATION SCRIPT
Check if your weather server is properly set up BEFORE testing!
"""

import sys
import os
from pathlib import Path
import subprocess


def validate_server_file():
    """Check if server file exists and is valid Python."""
    print("🦍 VALIDATING SERVER FILE")
    print("=" * 30)

    # Look for server files
    possible_files = [
        "weather_server.py",
        "server.py",
        "real_weather_mcp.py",
        "kingkong_weather.py",
    ]

    found_files = []
    for filename in possible_files:
        if Path(filename).exists():
            found_files.append(filename)
            print(f"✅ Found: {filename}")

    if not found_files:
        print("❌ NO SERVER FILE FOUND!")
        print("Available Python files in current directory:")
        for f in Path(".").glob("*.py"):
            print(f"   - {f.name}")
        print("\n💡 Make sure your server file is named 'weather_server.py'")
        return None

    # Use the first found file
    server_file = found_files[0]

    # Check if it's valid Python
    print(f"\n🐍 Checking Python syntax for {server_file}...")
    try:
        result = subprocess.run(
            [sys.executable, "-m", "py_compile", server_file],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            print("✅ Python syntax is valid")
        else:
            print("❌ Python syntax errors found:")
            print(result.stderr)
            return None
    except Exception as e:
        print(f"❌ Could not validate syntax: {e}")
        return None

    # Check if it contains FastMCP imports
    print(f"\n📦 Checking FastMCP imports...")
    try:
        with open(server_file, "r", encoding="utf-8") as f:
            content = f.read()

        if "from fastmcp import FastMCP" in content or "import fastmcp" in content:
            print("✅ FastMCP imports found")
        else:
            print("⚠️  No FastMCP imports found - might not be an MCP server")

        if "def " in content and "@mcp.tool" in content:
            print("✅ MCP tools defined")
        else:
            print("⚠️  No MCP tools found")

    except Exception as e:
        print(f"❌ Could not read file: {e}")
        return None

    return server_file


def test_direct_execution(server_file):
    """Test if server can run directly."""
    print(f"\n🚀 TESTING DIRECT EXECUTION: {server_file}")
    print("-" * 40)

    print("⚡ Starting server in test mode...")
    print("   (Will timeout after 5 seconds)")

    try:
        # Run server with timeout
        result = subprocess.run(
            [sys.executable, server_file], capture_output=True, text=True, timeout=5
        )

        print("⚠️  Server started but timed out (expected)")
        if result.stdout:
            print("📤 Server output:")
            print(result.stdout[:500])  # First 500 chars

    except subprocess.TimeoutExpired:
        print("✅ Server started successfully (timed out as expected)")
        return True
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        if hasattr(e, "stdout") and e.stdout:
            print("Error output:", e.stdout)
        if hasattr(e, "stderr") and e.stderr:
            print("Error details:", e.stderr)
        return False

    return True


def check_dependencies():
    """Check if all required packages are installed."""
    print("\n📦 CHECKING DEPENDENCIES")
    print("-" * 25)

    required_packages = [
        ("fastmcp", "FastMCP framework"),
        ("httpx", "HTTP client for API calls"),
        ("asyncio", "Async support (built-in)"),
    ]

    all_good = True

    for package, description in required_packages:
        try:
            if package == "asyncio":
                import asyncio
            else:
                __import__(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            print(f"❌ {package}: {description} - NOT INSTALLED")
            if package != "asyncio":
                print(f"   Install with: pip install {package}")
            all_good = False

    return all_good


def main():
    """Main validation function."""
    print("🦍 KING KONG'S SERVER VALIDATOR")
    print("=" * 40)
    print("Let's make sure everything is set up correctly!")
    print()

    # Step 1: Check dependencies
    deps_ok = check_dependencies()

    # Step 2: Find and validate server file
    server_file = validate_server_file()

    # Step 3: Test direct execution
    if server_file:
        execution_ok = test_direct_execution(server_file)
    else:
        execution_ok = False

    # Final summary
    print("\n" + "=" * 40)
    print("🦍 KING KONG'S FINAL DIAGNOSIS:")
    print("=" * 40)

    if deps_ok and server_file and execution_ok:
        print("✅ ALL CHECKS PASSED!")
        print("🚀 Your server is ready for testing!")
        print(f"📁 Server file: {server_file}")
        print("\nNext steps:")
        print("1. Set your API key: export OPENWEATHER_API_KEY='your_key'")
        print("2. Run the debug test: python debug_weather_test.py")
        print("3. Choose option 1 for quick connection test")
    else:
        print("❌ ISSUES FOUND!")
        print("\n🔧 Fix these issues:")
        if not deps_ok:
            print("- Install missing dependencies")
        if not server_file:
            print("- Create or fix your weather_server.py file")
        if not execution_ok:
            print("- Fix server startup errors")

        print(f"\n💡 Need help? Check the server file in your editor")
        print("💡 Make sure you copied the complete weather server code")


if __name__ == "__main__":
    main()
