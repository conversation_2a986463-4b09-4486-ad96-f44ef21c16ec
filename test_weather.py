#!/usr/bin/env python3
"""
Test Script for King Kong's REAL Weather MCP Server
🦍 WORKSHOP DEMO READY!
"""

import asyncio
import os
from fastmcp import Client


async def demo_weather_mcp():
    """
    Demo script to show off the weather MCP server capabilities.
    Perfect for workshop presentation!
    """
    print("🦍 KING KONG'S WEATHER MCP DEMONSTRATION")
    print("=" * 60)
    print("🌤️  TESTING REAL OPENWEATHERMAP API INTEGRATION")
    print("=" * 60)

    # Check if API key is set
    api_key = os.getenv("OPENWEATHER_API_KEY")
    if not api_key:
        print("⚠️  WARNING: OPENWEATHER_API_KEY not set!")
        print("   Get your free API key from: https://openweathermap.org/api")
        print("   Set it with: export OPENWEATHER_API_KEY=your_key_here")
        print()

    # Connect to the weather server
    client = Client("weather_server.py")  # Update filename as needed

    print("🔗 Connecting to King Kong's Weather MCP Server...")

    try:
        async with client:
            print("✅ Connected to King Kong's Weather MCP Server!")
            print()

            # Demo 1: Check API Status
            print("🔍 DEMO 1: Checking API Status...")
            print("-" * 40)
            status_result = await client.call_tool("check_api_status", {})
            print(f"API Status: {status_result[0].text}")
            print()

            # Demo 2: Get weather for major cities
            print("🌍 DEMO 2: Weather for Major Cities...")
            print("-" * 40)

            cities_to_test = [
                ("London", "GB"),
                ("New York", "US"),
                ("Tokyo", "JP"),
                ("Sydney", "AU"),
            ]

            for city, country in cities_to_test:
                print(f"🌤️  Getting weather for {city}, {country}...")
                try:
                    weather_result = await client.call_tool(
                        "get_current_weather",
                        {"city": city, "country_code": country, "units": "celsius"},
                    )
                    print(f"   Result: {weather_result[0].text}")
                    print()
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    print()

            # Demo 3: Weather by coordinates (Empire State Building)
            print("📍 DEMO 3: Weather by Coordinates...")
            print("-" * 40)
            print("🏢 Getting weather for Empire State Building coordinates...")
            coords_result = await client.call_tool(
                "get_weather_by_coordinates",
                {"latitude": 40.748817, "longitude": -73.985428, "units": "fahrenheit"},
            )
            print(f"Coordinates Weather: {coords_result[0].text}")
            print()

            # Demo 4: Quick weather summary
            print("📊 DEMO 4: Quick Weather Summary...")
            print("-" * 40)
            summary_result = await client.call_tool(
                "get_weather_summary", {"city": "Paris"}
            )
            print(f"Paris Summary: {summary_result[0].text}")
            print()

            # Demo 5: Different temperature units
            print("🌡️  DEMO 5: Different Temperature Units...")
            print("-" * 40)

            units_to_test = ["celsius", "fahrenheit", "kelvin"]
            for unit in units_to_test:
                print(f"Temperature in {unit.title()}:")
                unit_result = await client.call_tool(
                    "get_current_weather", {"city": "Berlin", "units": unit}
                )
                print(f"   {unit_result[0].text}")
                print()

            # Demo 6: Test resources
            print("📚 DEMO 6: Available Resources...")
            print("-" * 40)
            resources = await client.list_resources()
            for resource in resources:
                print(f"📄 {resource.uri}: {resource.name}")
            print()

            # Read API config resource
            print("⚙️  Reading API Configuration...")
            config_result = await client.read_resource("config://weather-api")
            print(f"Config: {config_result[0].text}")
            print()

            # Demo 7: Error handling (invalid city)
            print("❌ DEMO 7: Error Handling...")
            print("-" * 40)
            print("Testing with invalid city name...")
            error_result = await client.call_tool(
                "get_current_weather", {"city": "InvalidCityXYZ123"}
            )
            print(f"Error Response: {error_result[0].text}")
            print()

            print("🎉" * 20)
            print("🦍 ALL DEMOS COMPLETED SUCCESSFULLY!")
            print("🦍 KING KONG'S WEATHER MCP IS WORKSHOP READY!")
            print("🎉" * 20)

    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("💡 Make sure:")
        print("   1. Your weather server file is named correctly")
        print("   2. OPENWEATHER_API_KEY environment variable is set")
        print("   3. You have internet connection")
        print("   4. FastMCP is installed: pip install fastmcp")


async def quick_test():
    """Quick test for workshop prep - just test one city."""
    print("🦍 QUICK WEATHER TEST")
    print("=" * 30)

    client = Client("weather_server.py")

    try:
        async with client:
            # Just test London weather
            result = await client.call_tool(
                "get_current_weather", {"city": "London", "country_code": "GB"}
            )
            print("✅ SUCCESS! Weather MCP is working:")
            print(result[0].text)
    except Exception as e:
        print(f"❌ FAILED: {e}")


if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Full demo (for workshop presentation)")
    print("2. Quick test (just verify it works)")

    choice = input("Enter 1 or 2: ").strip()

    if choice == "2":
        asyncio.run(quick_test())
    else:
        asyncio.run(demo_weather_mcp())
